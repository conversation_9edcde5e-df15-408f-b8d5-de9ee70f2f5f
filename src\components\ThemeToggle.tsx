'use client';

import { useAppDispatch, useAppSelector } from '../store';
import { setTheme, ThemeMode } from '../store/slices/themeSlice';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export default function ThemeToggle() {
  const dispatch = useAppDispatch();
  const { mode: currentMode, isDark } = useAppSelector((state) => state.theme);
  const { t } = useTranslation();
  const [mode, setMode] = useState<ThemeMode | null>(null);

  useEffect(() => {
    // Read theme from localStorage or system
    const theme = localStorage.getItem('theme');
    const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (theme === 'dark' || (theme === 'system' && systemDark) || (!theme && systemDark)) {
      setMode('dark');
    } else {
      setMode('light');
    }
  }, []);

  const handleThemeChange = (newMode: ThemeMode) => {
    dispatch(setTheme(newMode));
  };

  if (mode === null) return null; // Prevent hydration mismatch

  return (
    <div className="flex items-center space-x-2 p-2 bg-card rounded-lg border">
      <span className="text-sm font-medium text-foreground">
        {t('theme.toggle')}:
      </span>
      <div className="flex space-x-1">
        <button
          onClick={() => handleThemeChange('light')}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            currentMode === 'light'
              ? 'bg-primary text-primary-foreground'
              : 'bg-secondary text-secondary-foreground hover:bg-accent'
          }`}
        >
          ☀️ {t('theme.light')}
        </button>
        <button
          onClick={() => handleThemeChange('dark')}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            currentMode === 'dark'
              ? 'bg-primary text-primary-foreground'
              : 'bg-secondary text-secondary-foreground hover:bg-accent'
          }`}
        >
          🌙 {t('theme.dark')}
        </button>
        <button
          onClick={() => handleThemeChange('system')}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            currentMode === 'system'
              ? 'bg-primary text-primary-foreground'
              : 'bg-secondary text-secondary-foreground hover:bg-accent'
          }`}
        >
          💻 {t('theme.system')}
        </button>
      </div>
      <div className="text-xs text-muted-foreground">
        ({isDark ? t('theme.dark') : t('theme.light')})
      </div>
    </div>
  );
}
