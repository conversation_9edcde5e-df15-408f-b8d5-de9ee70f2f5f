'use client';

import { useTranslation } from 'react-i18next';
import Header from '../components/Header';
import '../lib/i18n'; // Initialize i18n

export default function Home() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <section className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl  font-bold text-foreground mb-6 animate-fade-in">
            {t('home.title')}
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-4 animate-slide-in">
            {t('home.subtitle')}
          </p>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8 animate-slide-in">
            {t('home.description')}
          </p>
          <p className='text-2xl text-amber-500 dark:text-blue-500'> new</p>

          <div className="flex gap-4 justify-center flex-col sm:flex-row animate-bounce-subtle">
            <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
              {t('home.getStarted')}
            </button>
            <button className="border border-border text-foreground px-8 py-3 rounded-lg font-medium hover:bg-accent transition-colors">
              {t('home.learnMore')}
            </button>
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">
            {t('home.features.title')}
          </h2>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-card p-6 rounded-lg border hover:shadow-lg transition-shadow">
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-semibold text-card-foreground mb-3">
                {t('home.features.feature1.title')}
              </h3>
              <p className="text-muted-foreground">
                {t('home.features.feature1.description')}
              </p>
            </div>

            <div className="bg-card p-6 rounded-lg border hover:shadow-lg transition-shadow">
              <div className="text-4xl mb-4">💻</div>
              <h3 className="text-xl font-semibold text-card-foreground mb-3">
                {t('home.features.feature2.title')}
              </h3>
              <p className="text-muted-foreground">
                {t('home.features.feature2.description')}
              </p>
            </div>

            <div className="bg-card p-6 rounded-lg border hover:shadow-lg transition-shadow">
              <div className="text-4xl mb-4">📈</div>
              <h3 className="text-xl font-semibold text-card-foreground mb-3">
                {t('home.features.feature3.title')}
              </h3>
              <p className="text-muted-foreground">
                {t('home.features.feature3.description')}
              </p>
            </div>
          </div>
        </section>

        {/* Demo Section */}
        <section className="bg-card p-8 rounded-lg border">
          <h2 className="text-2xl font-bold text-card-foreground mb-6 text-center">
            🎨 Theme & Language Demo
          </h2>
          <div className="space-y-4">
            <p className="text-muted-foreground text-center">
              Try switching between different themes and languages to see the changes in real-time!
            </p>
            <div className="flex flex-wrap gap-4 justify-center items-center">
              <div className="bg-primary/10 text-primary px-4 py-2 rounded-lg">
                Current Theme: <span className="font-semibold">Dynamic</span>
              </div>
              <div className="bg-secondary/10 text-secondary-foreground px-4 py-2 rounded-lg">
                Current Language: <span className="font-semibold">Multi-lingual</span>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-card/50 py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground mb-4">
            {t('footer.copyright')}
          </p>
          <div className="flex justify-center space-x-6">
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
              {t('footer.privacy')}
            </a>
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
              {t('footer.terms')}
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
}
