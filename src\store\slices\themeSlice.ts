import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
}

// Function to get system preference
const getSystemTheme = (): boolean => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return false;
};

// Function to get stored theme or default to system
const getInitialTheme = (): ThemeMode => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('theme') as ThemeMode;
    if (stored && ['light', 'dark', 'system'].includes(stored)) {
      return stored;
    }
  }
  return 'system';
};

// Function to determine if dark mode should be active
const getIsDark = (mode: ThemeMode): boolean => {
  switch (mode) {
    case 'dark':
      return true;
    case 'light':
      return false;
    case 'system':
      return getSystemTheme();
    default:
      return false;
  }
};

const initialState: ThemeState = {
  mode: getInitialTheme(),
  isDark: getIsDark(getInitialTheme()),
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
      state.isDark = getIsDark(action.payload);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', action.payload);
        
        // Apply theme to document
        const root = document.documentElement;
        if (state.isDark) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    },
    toggleTheme: (state) => {
      const newMode = state.mode === 'dark' ? 'light' : 'dark';
      state.mode = newMode;
      state.isDark = getIsDark(newMode);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', newMode);
        
        // Apply theme to document
        const root = document.documentElement;
        if (state.isDark) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    },
    initializeTheme: (state) => {
      // This action is called on app initialization to sync with system/localStorage
      state.mode = getInitialTheme();
      state.isDark = getIsDark(state.mode);
      
      if (typeof window !== 'undefined') {
        const root = document.documentElement;
        if (state.isDark) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    },
  },
});

export const { setTheme, toggleTheme, initializeTheme } = themeSlice.actions;
export default themeSlice.reducer;
