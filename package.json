{"name": "marketing-agency", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "i18next": "^25.4.2", "i18next-browser-languagedetector": "^8.2.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^15.7.2", "react-redux": "^9.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}