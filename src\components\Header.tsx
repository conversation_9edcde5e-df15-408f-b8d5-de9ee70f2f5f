'use client';

import { useTranslation } from 'react-i18next';
import ThemeToggle from './ThemeToggle';
import LanguageSwitcher from './LanguageSwitcher';

export default function Header() {
  const { t } = useTranslation();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-foreground">
            {t('home.title')}
          </h1>
        </div>

        <nav className="hidden md:flex items-center space-x-6">
          <a href="#" className="text-sm font-medium text-foreground hover:text-primary transition-colors">
            {t('navigation.home')}
          </a>
          <a href="#" className="text-sm font-medium text-foreground hover:text-primary transition-colors">
            {t('navigation.about')}
          </a>
          <a href="#" className="text-sm font-medium text-foreground hover:text-primary transition-colors">
            {t('navigation.services')}
          </a>
          <a href="/contact" className="text-sm font-medium text-foreground hover:text-primary transition-colors">
            {t('navigation.contact')}
          </a>
        </nav>

        <div className="flex items-center space-x-4">
          <LanguageSwitcher />
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}
