'use client';

import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store';
import { initializeTheme } from '../store/slices/themeSlice';
import { initializeLanguage } from '../store/slices/languageSlice';

export default function ClientInitializer() {
  const dispatch = useAppDispatch();
  const { isDark } = useAppSelector((state) => state.theme);

  useEffect(() => {
    // Initialize theme and language on client side
    dispatch(initializeTheme());
    dispatch(initializeLanguage());
  }, [dispatch]);

  // Apply theme class immediately when isDark changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement;
      if (isDark) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }
  }, [isDark]);

  return null; // This component doesn't render anything
}
