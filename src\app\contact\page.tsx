'use client';

import { useTranslation } from 'react-i18next';
import Header from '../../components/Header';
import '../../lib/i18n';

export default function ContactPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-foreground mb-8 text-center">
          {t('navigation.contact')}
        </h1>

        {/* Dark Mode Test Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-foreground mb-6">
            🎨 Dark Mode Test
          </h2>

          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
              <p className="text-gray-900 dark:text-gray-100 mb-2">
                This text adapts to theme: Dark background in dark mode, white in light mode
              </p>
              <p className="text-amber-500 dark:text-blue-500 font-semibold">
                Amber in light mode → Blue in dark mode
              </p>
            </div>

            <div className="bg-gray-100 dark:bg-gray-900 p-6 rounded-lg">
              <p className="text-green-600 dark:text-green-400">Success message color</p>
              <p className="text-red-600 dark:text-green-400">Error message color</p>
              <p className="text-blue-600 dark:text-blue-400">Info message color</p>
            </div>

            <div className="border-2 border-gray-300 dark:border-gray-600 p-6 rounded-lg">
              <p className="text-foreground">
                This uses our custom CSS variables that adapt automatically
              </p>
              <div className="mt-4 p-4 bg-card border rounded">
                <p className="text-card-foreground">Card with adaptive colors</p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="max-w-2xl mx-auto">
          <div className="bg-card p-8 rounded-lg border">
            <h3 className="text-xl font-semibold text-card-foreground mb-6">
              Contact Form
            </h3>

            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="Your name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Message
                </label>
                <textarea
                  rows={4}
                  className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="Your message..."
                />
              </div>

              <button
                type="submit"
                className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 transition-colors"
              >
                Send Message
              </button>
            </form>
          </div>
        </section>
      </main>
    </div>
  );
}
